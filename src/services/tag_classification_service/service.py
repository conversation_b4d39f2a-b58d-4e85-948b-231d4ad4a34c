"""
标签和分类服务模块（重构版）
包含标签管理、分类管理、用户兴趣等核心业务逻辑
"""

import json
import logging
from datetime import date, datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, asc, desc, func, or_, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload, selectinload

from .models import (ClassificationDimension, ClassificationValue, Tag,
                     TagClassification, TagRelationship,
                     UserClassificationPreference, UserInterestTag,
                     UserProfileSnapshot)
from .schemas import (ClassificationDimensionCreate,
                      ClassificationDimensionUpdate, ClassificationValueCreate,
                      ClassificationValueUpdate, LifecycleStage,
                      TagClassificationCreate, TagClassificationUpdate, TagCreate,
                      TagListQuery, TagUpdate,
                      UserClassificationPreferenceCreate,
                      UserClassificationPreferenceUpdate)


class TagClassificationService:
    """标签分类服务（合并原TagTypeService和TagCategoryService）"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def create_classification(self, classification_data: TagClassificationCreate) -> TagClassification:
        """
        创建标签分类

        Args:
            classification_data: 标签分类创建数据

        Returns:
            创建的标签分类对象

        Raises:
            ValueError: 当分类代码已存在时
        """
        try:
            # 检查代码是否已存在
            existing = (
                self.db.query(TagClassification)
                .filter(TagClassification.classification_code == classification_data.classification_code)
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification with code '{classification_data.classification_code}' already exists"
                )

            # 计算层级和路径
            level = 1
            path = classification_data.classification_code
            if classification_data.parent_id:
                parent = self.get_classification_by_id(classification_data.parent_id)
                if parent:
                    level = parent.level + 1
                    path = f"{parent.path}.{classification_data.classification_code}"

            # 创建新分类
            classification = TagClassification(
                classification_code=classification_data.classification_code,
                classification_name=classification_data.classification_name,
                parent_id=classification_data.parent_id,
                level=level,
                path=path,
                description=classification_data.description,
                icon=classification_data.icon,
                color=classification_data.color,
                sort_order=classification_data.sort_order,
            )

            self.db.add(classification)
            self.db.commit()
            self.db.refresh(classification)

            logging.info(f"Created classification: {classification.classification_code}")
            return classification

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification: {e}")
            raise ValueError("Failed to create classification due to database constraints")

    def get_classification_by_id(self, classification_id: int) -> Optional[TagClassification]:
        """
        根据ID获取标签分类

        Args:
            classification_id: 分类ID

        Returns:
            分类对象或None
        """
        return self.db.query(TagClassification).filter(TagClassification.id == classification_id).first()

    def get_classification_by_code(self, classification_code: str) -> Optional[TagClassification]:
        """
        根据代码获取标签分类

        Args:
            classification_code: 分类代码

        Returns:
            分类对象或None
        """
        return (
            self.db.query(TagClassification)
            .filter(TagClassification.classification_code == classification_code)
            .first()
        )

    def get_classifications(
        self,
        parent_id: Optional[int] = None,
        is_active: bool = True,
        skip: int = 0,
        limit: int = 100,
    ) -> List[TagClassification]:
        """
        获取分类列表

        Args:
            parent_id: 父分类ID过滤
            is_active: 是否只返回活跃分类
            skip: 跳过记录数
            limit: 限制记录数

        Returns:
            分类列表
        """
        query = self.db.query(TagClassification)

        if parent_id is not None:
            query = query.filter(TagClassification.parent_id == parent_id)
        if is_active:
            query = query.filter(TagClassification.is_active == True)

        return query.order_by(TagClassification.sort_order.desc(), TagClassification.id).offset(skip).limit(limit).all()

    def update_classification(
        self, classification_id: int, classification_data: TagClassificationUpdate
    ) -> Optional[TagClassification]:
        """
        更新标签分类

        Args:
            classification_id: 分类ID
            classification_data: 更新数据

        Returns:
            更新后的分类对象或None
        """
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return None

        try:
            # 更新字段
            update_data = classification_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(classification, field):
                    setattr(classification, field, value)

            # 如果更新了父分类，重新计算层级和路径
            if "parent_id" in update_data:
                if classification.parent_id:
                    parent = self.get_classification_by_id(classification.parent_id)
                    if parent:
                        classification.level = parent.level + 1
                        classification.path = f"{parent.path}.{classification.classification_code}"
                else:
                    classification.level = 1
                    classification.path = classification.classification_code

            self.db.commit()
            self.db.refresh(classification)

            logging.info(f"Updated classification: {classification.classification_code}")
            return classification

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification: {e}")
            raise ValueError("Failed to update classification due to database constraints")

    def delete_classification(self, classification_id: int) -> bool:
        """
        删除标签分类

        Args:
            classification_id: 分类ID

        Returns:
            是否删除成功
        """
        classification = self.get_classification_by_id(classification_id)
        if not classification:
            return False

        try:
            # 检查是否有子分类
            children_count = (
                self.db.query(TagClassification)
                .filter(TagClassification.parent_id == classification_id)
                .count()
            )
            if children_count > 0:
                raise ValueError("Cannot delete classification with child classifications")

            # 检查是否有关联的标签
            tags_count = (
                self.db.query(Tag)
                .filter(Tag.classification_id == classification_id)
                .count()
            )
            if tags_count > 0:
                raise ValueError("Cannot delete classification with associated tags")

            self.db.delete(classification)
            self.db.commit()

            logging.info(f"Deleted classification: {classification.classification_code}")
            return True

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification: {e}")
            raise


class TagService:
    """标签服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    def create_tag(self, tag_data: TagCreate) -> Tag:
        """创建标签"""
        try:
            # 检查代码和slug的唯一性
            if self.db.query(Tag).filter(Tag.tag_code == tag_data.tag_code).first():
                raise ValueError(f"Tag with code '{tag_data.tag_code}' already exists")

            if self.db.query(Tag).filter(Tag.tag_slug == tag_data.tag_slug).first():
                raise ValueError(f"Tag with slug '{tag_data.tag_slug}' already exists")

            # 验证分类存在性
            if (
                not self.db.query(TagClassification)
                .filter(TagClassification.id == tag_data.classification_id)
                .first()
            ):
                raise ValueError(f"Classification with ID {tag_data.classification_id} not found")

            # 处理父标签和层级
            level = 1
            path = tag_data.tag_code
            if tag_data.parent_id:
                parent = self.get_tag_by_id(tag_data.parent_id)
                if not parent:
                    raise ValueError(
                        f"Parent tag with ID {tag_data.parent_id} not found"
                    )
                level = parent.level + 1
                path = f"{parent.path}.{tag_data.tag_code}"

            # 处理synonyms字段，转换为JSON字符串以兼容SQLite
            synonyms_json = None
            if tag_data.synonyms:
                synonyms_json = (
                    json.dumps(tag_data.synonyms)
                    if isinstance(tag_data.synonyms, list)
                    else tag_data.synonyms
                )

            tag = Tag(
                tag_name=tag_data.tag_name,
                tag_code=tag_data.tag_code,
                tag_slug=tag_data.tag_slug,
                parent_id=tag_data.parent_id,
                level=level,
                path=path,
                classification_id=tag_data.classification_id,
                color=tag_data.color,
                icon=tag_data.icon,
                base_weight=tag_data.base_weight,
                description=tag_data.description,
                synonyms=synonyms_json,
                lifecycle_stage=tag_data.lifecycle_stage,
                is_system=False,  # 用户创建的标签默认不是系统标签
            )

            self.db.add(tag)
            self.db.commit()
            self.db.refresh(tag)

            logging.info(f"Created tag: {tag.tag_code}")
            return tag

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create tag: {e}")
            raise ValueError("Failed to create tag due to database constraints")

    def get_tag_by_id(
        self, tag_id: int, include_relations: bool = False
    ) -> Optional[Tag]:
        """根据ID获取标签"""
        query = self.db.query(Tag)

        if include_relations:
            query = query.options(
                joinedload(Tag.classification),
                joinedload(Tag.parent),
                selectinload(Tag.children),
            )

        return query.filter(Tag.id == tag_id).first()

    def get_tag_by_code(self, tag_code: str) -> Optional[Tag]:
        """根据代码获取标签"""
        return self.db.query(Tag).filter(Tag.tag_code == tag_code).first()

    def search_tags(self, query: str, limit: int = 10) -> List[Tag]:
        """搜索标签"""
        return (
            self.db.query(Tag)
            .filter(
                and_(
                    Tag.is_active == True,
                    or_(
                        Tag.tag_name.ilike(f"%{query}%"),
                        Tag.tag_code.ilike(f"%{query}%"),
                        Tag.description.ilike(f"%{query}%"),
                    ),
                )
            )
            .order_by(Tag.usage_count.desc())
            .limit(limit)
            .all()
        )

    def list_tags(self, query: TagListQuery) -> Tuple[List[Tag], int]:
        """获取标签列表"""
        db_query = self.db.query(Tag)

        # 应用过滤条件
        if query.search:
            search_filter = or_(
                Tag.tag_name.ilike(f"%{query.search}%"),
                Tag.tag_code.ilike(f"%{query.search}%"),
                Tag.description.ilike(f"%{query.search}%"),
            )
            db_query = db_query.filter(search_filter)

        if query.classification_id:
            db_query = db_query.filter(Tag.classification_id == query.classification_id)

        if query.parent_id is not None:
            db_query = db_query.filter(Tag.parent_id == query.parent_id)

        if query.is_active is not None:
            db_query = db_query.filter(Tag.is_active == query.is_active)

        if query.lifecycle_stage:
            db_query = db_query.filter(Tag.lifecycle_stage == query.lifecycle_stage)

        # 计算总数
        total = db_query.count()

        # 应用分页和排序
        skip = (query.page - 1) * query.size
        tags = (
            db_query.order_by(Tag.usage_count.desc(), Tag.created_at.desc())
            .offset(skip)
            .limit(query.size)
            .all()
        )

        return tags, total

    def update_tag(self, tag_id: int, update_data: TagUpdate) -> Optional[Tag]:
        """更新标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                if field == "synonyms" and value:
                    # 处理synonyms字段
                    value = json.dumps(value) if isinstance(value, list) else value
                if hasattr(tag, field):
                    setattr(tag, field, value)

            self.db.commit()
            self.db.refresh(tag)

            logging.info(f"Updated tag: {tag.tag_code}")
            return tag

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update tag: {e}")
            raise ValueError("Failed to update tag due to database constraints")

    def delete_tag(self, tag_id: int) -> bool:
        """删除标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        try:
            # 检查是否有子标签
            children_count = (
                self.db.query(Tag)
                .filter(Tag.parent_id == tag_id)
                .count()
            )
            if children_count > 0:
                raise ValueError("Cannot delete tag with child tags")

            self.db.delete(tag)
            self.db.commit()

            logging.info(f"Deleted tag: {tag.tag_code}")
            return True

        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete tag: {e}")
            raise

    def get_popular_tags(self, limit: int = 10) -> List[Tag]:
        """获取热门标签"""
        return (
            self.db.query(Tag)
            .filter(Tag.is_active == True)
            .order_by(Tag.usage_count.desc())
            .limit(limit)
            .all()
        )

    def get_trending_tags(self, limit: int = 10) -> List[Tag]:
        """获取趋势标签"""
        return (
            self.db.query(Tag)
            .filter(Tag.is_active == True)
            .order_by(Tag.daily_usage_count.desc())
            .limit(limit)
            .all()
        )

    def activate_tag(self, tag_id: int) -> bool:
        """激活标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        tag.is_active = True
        self.db.commit()
        return True

    def deactivate_tag(self, tag_id: int) -> bool:
        """停用标签"""
        tag = self.get_tag_by_id(tag_id)
        if not tag:
            return False

        tag.is_active = False
        self.db.commit()
        return True


class ClassificationService:
    """分类服务"""

    def __init__(self, db: Session):
        """初始化服务"""
        self.db = db

    # ==================== 分类维度操作 ====================

    def create_classification_dimension(
        self, dimension_data: ClassificationDimensionCreate
    ) -> ClassificationDimension:
        """
        创建分类维度

        Args:
            dimension_data: 分类维度创建数据

        Returns:
            创建的分类维度对象

        Raises:
            ValueError: 当维度名称已存在时
        """
        try:
            # 检查维度名称是否已存在
            existing = (
                self.db.query(ClassificationDimension)
                .filter(
                    ClassificationDimension.dimension_name
                    == dimension_data.dimension_name
                )
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification dimension with name '{dimension_data.dimension_name}' already exists"
                )

            # 创建新维度
            dimension = ClassificationDimension(
                dimension_name=dimension_data.dimension_name,
                display_name=dimension_data.display_name,
                description=dimension_data.description,
                sort_order=dimension_data.sort_order,
            )

            self.db.add(dimension)
            self.db.commit()
            self.db.refresh(dimension)

            logging.info(
                f"Created classification dimension: {dimension.dimension_name}"
            )
            return dimension

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification dimension: {e}")
            raise ValueError(
                "Failed to create classification dimension due to database constraints"
            )

    def get_classification_dimension_by_id(
        self, dimension_id: int
    ) -> Optional[ClassificationDimension]:
        """
        根据ID获取分类维度

        Args:
            dimension_id: 分类维度ID

        Returns:
            分类维度对象或None
        """
        return (
            self.db.query(ClassificationDimension)
            .filter(ClassificationDimension.id == dimension_id)
            .first()
        )

    def get_classification_dimension_by_name(
        self, dimension_name: str
    ) -> Optional[ClassificationDimension]:
        """
        根据名称获取分类维度

        Args:
            dimension_name: 分类维度名称

        Returns:
            分类维度对象或None
        """
        return (
            self.db.query(ClassificationDimension)
            .filter(ClassificationDimension.dimension_name == dimension_name)
            .first()
        )

    def get_classification_dimensions(
        self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
    ) -> Tuple[List[ClassificationDimension], int]:
        """
        获取分类维度列表

        Args:
            skip: 跳过的记录数
            limit: 返回的记录数
            is_active: 是否只获取活跃的维度

        Returns:
            分类维度列表和总数的元组
        """
        query = self.db.query(ClassificationDimension)

        if is_active is not None:
            query = query.filter(ClassificationDimension.is_active == is_active)

        total = query.count()

        dimensions = (
            query.order_by(
                ClassificationDimension.sort_order.desc(),
                ClassificationDimension.dimension_name,
            )
            .offset(skip)
            .limit(limit)
            .all()
        )

        return dimensions, total

    def update_classification_dimension(
        self, dimension_id: int, update_data: ClassificationDimensionUpdate
    ) -> Optional[ClassificationDimension]:
        """
        更新分类维度

        Args:
            dimension_id: 分类维度ID
            update_data: 更新数据

        Returns:
            更新后的分类维度对象或None
        """
        dimension = self.get_classification_dimension_by_id(dimension_id)
        if not dimension:
            return None

        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(dimension, field, value)

        try:
            self.db.commit()
            self.db.refresh(dimension)
            logging.info(
                f"Updated classification dimension: {dimension.dimension_name}"
            )
            return dimension
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification dimension: {e}")
            raise ValueError(
                "Failed to update classification dimension due to database constraints"
            )

    def delete_classification_dimension(self, dimension_id: int) -> bool:
        """
        删除分类维度

        Args:
            dimension_id: 分类维度ID

        Returns:
            是否删除成功
        """
        dimension = self.get_classification_dimension_by_id(dimension_id)
        if not dimension:
            return False

        # 检查是否有关联的分类值
        value_count = (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.dimension_id == dimension_id)
            .count()
        )
        if value_count > 0:
            raise ValueError(
                f"Cannot delete dimension: {value_count} classification values are using this dimension"
            )

        # 检查是否有关联的用户偏好
        preference_count = (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.dimension_id == dimension_id)
            .count()
        )
        if preference_count > 0:
            raise ValueError(
                f"Cannot delete dimension: {preference_count} user preferences are using this dimension"
            )

        try:
            self.db.delete(dimension)
            self.db.commit()
            logging.info(
                f"Deleted classification dimension: {dimension.dimension_name}"
            )
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification dimension: {e}")
            return False

    # ==================== 分类值操作 ====================

    def create_classification_value(
        self, value_data: ClassificationValueCreate
    ) -> ClassificationValue:
        """
        创建分类值

        Args:
            value_data: 分类值创建数据

        Returns:
            创建的分类值对象

        Raises:
            ValueError: 当分类值代码已存在时
        """
        try:
            # 检查分类值代码在同一维度下是否已存在
            existing = (
                self.db.query(ClassificationValue)
                .filter(
                    and_(
                        ClassificationValue.dimension_id == value_data.dimension_id,
                        ClassificationValue.value_code == value_data.value_code,
                    )
                )
                .first()
            )

            if existing:
                raise ValueError(
                    f"Classification value with code '{value_data.value_code}' already exists in this dimension"
                )

            # 验证维度是否存在
            dimension = self.get_classification_dimension_by_id(value_data.dimension_id)
            if not dimension:
                raise ValueError(
                    f"Classification dimension with ID {value_data.dimension_id} does not exist"
                )

            # 计算层级
            level = 1
            if value_data.parent_id:
                parent = (
                    self.db.query(ClassificationValue)
                    .filter(ClassificationValue.id == value_data.parent_id)
                    .first()
                )
                if not parent:
                    raise ValueError(
                        f"Parent classification value with ID {value_data.parent_id} does not exist"
                    )
                if parent.dimension_id != value_data.dimension_id:
                    raise ValueError(
                        "Parent classification value must be in the same dimension"
                    )
                level = parent.level + 1

            # 创建新分类值
            value = ClassificationValue(
                dimension_id=value_data.dimension_id,
                value_code=value_data.value_code,
                display_name=value_data.display_name,
                description=value_data.description,
                parent_id=value_data.parent_id,
                level=level,
                sort_order=value_data.sort_order,
            )

            self.db.add(value)
            self.db.commit()
            self.db.refresh(value)

            logging.info(f"Created classification value: {value.value_code}")
            return value

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create classification value: {e}")
            raise ValueError(
                "Failed to create classification value due to database constraints"
            )

    def get_classification_value_by_id(
        self, value_id: int
    ) -> Optional[ClassificationValue]:
        """
        根据ID获取分类值

        Args:
            value_id: 分类值ID

        Returns:
            分类值对象或None
        """
        return (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.id == value_id)
            .first()
        )

    def get_classification_values(
        self,
        dimension_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100,
        parent_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
    ) -> Tuple[List[ClassificationValue], int]:
        """
        获取分类值列表

        Args:
            dimension_id: 分类维度ID，为None表示获取所有维度的分类值
            skip: 跳过的记录数
            limit: 返回的记录数
            parent_id: 父分类值ID，为None表示获取所有层级
            is_active: 是否只获取活跃的分类值
            search: 搜索关键词，支持按display_name模糊搜索

        Returns:
            分类值列表和总数的元组
        """
        query = self.db.query(ClassificationValue)

        if dimension_id is not None:
            query = query.filter(ClassificationValue.dimension_id == dimension_id)

        if parent_id is not None:
            query = query.filter(ClassificationValue.parent_id == parent_id)

        if is_active is not None:
            query = query.filter(ClassificationValue.is_active == is_active)

        if search is not None and search.strip():
            # 支持按display_name模糊搜索
            search_term = f"%{search.strip()}%"
            query = query.filter(ClassificationValue.display_name.ilike(search_term))

        total = query.count()

        values = (
            query.order_by(
                ClassificationValue.sort_order.desc(), ClassificationValue.display_name
            )
            .offset(skip)
            .limit(limit)
            .all()
        )

        return values, total

    def get_classification_values_tree(
        self, dimension_id: int, is_active: Optional[bool] = None
    ) -> List[ClassificationValue]:
        """
        获取分类值的树形结构

        Args:
            dimension_id: 分类维度ID
            is_active: 是否只获取活跃的分类值

        Returns:
            分类值树形列表
        """
        query = self.db.query(ClassificationValue).filter(
            ClassificationValue.dimension_id == dimension_id
        )

        if is_active is not None:
            query = query.filter(ClassificationValue.is_active == is_active)

        all_values = query.order_by(
            ClassificationValue.level,
            ClassificationValue.sort_order.desc(),
            ClassificationValue.display_name,
        ).all()

        # 构建树形结构
        value_dict = {value.id: value for value in all_values}
        root_values = []

        for value in all_values:
            value.children = []
            if value.parent_id is None:
                root_values.append(value)
            else:
                parent = value_dict.get(value.parent_id)
                if parent:
                    if not hasattr(parent, "children"):
                        parent.children = []
                    parent.children.append(value)

        return root_values

    def update_classification_value(
        self, value_id: int, update_data: ClassificationValueUpdate
    ) -> Optional[ClassificationValue]:
        """
        更新分类值

        Args:
            value_id: 分类值ID
            update_data: 更新数据

        Returns:
            更新后的分类值对象或None
        """
        value = self.get_classification_value_by_id(value_id)
        if not value:
            return None

        # 处理父级更新
        if update_data.parent_id is not None:
            if update_data.parent_id == value_id:
                raise ValueError("Classification value cannot be its own parent")

            if self._would_create_cycle_in_values(value_id, update_data.parent_id):
                raise ValueError("Cannot update parent: would create a cycle")

        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value_to_set in update_dict.items():
            setattr(value, field, value_to_set)

        # 重新计算层级
        if update_data.parent_id is not None:
            if update_data.parent_id:
                parent = self.get_classification_value_by_id(update_data.parent_id)
                if parent:
                    value.level = parent.level + 1
                else:
                    raise ValueError("Parent classification value does not exist")
            else:
                value.level = 1

        try:
            self.db.commit()
            self.db.refresh(value)
            logging.info(f"Updated classification value: {value.value_code}")
            return value
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update classification value: {e}")
            raise ValueError(
                "Failed to update classification value due to database constraints"
            )

    def delete_classification_value(self, value_id: int) -> bool:
        """
        删除分类值

        Args:
            value_id: 分类值ID

        Returns:
            是否删除成功
        """
        value = self.get_classification_value_by_id(value_id)
        if not value:
            return False

        # 检查是否有子分类值
        child_count = (
            self.db.query(ClassificationValue)
            .filter(ClassificationValue.parent_id == value_id)
            .count()
        )
        if child_count > 0:
            raise ValueError(
                f"Cannot delete classification value: {child_count} child values depend on it"
            )

        # 检查是否有关联的用户偏好
        preference_count = (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.value_id == value_id)
            .count()
        )
        if preference_count > 0:
            raise ValueError(
                f"Cannot delete classification value: {preference_count} user preferences are using it"
            )

        try:
            self.db.delete(value)
            self.db.commit()
            logging.info(f"Deleted classification value: {value.value_code}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete classification value: {e}")
            return False

    def _would_create_cycle_in_values(self, value_id: int, new_parent_id: int) -> bool:
        """检查是否会创建循环依赖"""
        if new_parent_id == value_id:
            return True

        current_parent_id = new_parent_id
        while current_parent_id:
            parent = self.get_classification_value_by_id(current_parent_id)
            if not parent:
                break
            if parent.parent_id == value_id:
                return True
            current_parent_id = parent.parent_id

        return False

    # ==================== 用户分类偏好操作 ====================

    def create_user_classification_preference(
        self, preference_data: UserClassificationPreferenceCreate
    ) -> UserClassificationPreference:
        """
        创建用户分类偏好

        Args:
            preference_data: 用户分类偏好创建数据

        Returns:
            创建的用户分类偏好对象
        """
        try:
            # 检查是否已存在相同的偏好记录
            existing = (
                self.db.query(UserClassificationPreference)
                .filter(
                    and_(
                        UserClassificationPreference.user_id == preference_data.user_id,
                        UserClassificationPreference.dimension_id
                        == preference_data.dimension_id,
                        UserClassificationPreference.value_id
                        == preference_data.value_id,
                    )
                )
                .first()
            )

            if existing:
                # 更新现有记录
                existing.preference_score = Decimal(
                    str(preference_data.preference_score)
                )
                existing.source = preference_data.source
                existing.last_updated = datetime.now()
                self.db.commit()
                self.db.refresh(existing)
                return existing

            # 验证维度和值是否存在
            dimension = self.get_classification_dimension_by_id(
                preference_data.dimension_id
            )
            if not dimension:
                raise ValueError(
                    f"Classification dimension with ID {preference_data.dimension_id} does not exist"
                )

            value = self.get_classification_value_by_id(preference_data.value_id)
            if not value:
                raise ValueError(
                    f"Classification value with ID {preference_data.value_id} does not exist"
                )

            if value.dimension_id != preference_data.dimension_id:
                raise ValueError(
                    "Classification value must belong to the specified dimension"
                )

            # 创建新偏好记录
            preference = UserClassificationPreference(
                user_id=preference_data.user_id,
                dimension_id=preference_data.dimension_id,
                value_id=preference_data.value_id,
                preference_score=Decimal(str(preference_data.preference_score)),
                source=preference_data.source,
            )

            self.db.add(preference)
            self.db.commit()
            self.db.refresh(preference)

            logging.info(
                f"Created user classification preference for user {preference_data.user_id}"
            )
            return preference

        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to create user classification preference: {e}")
            raise ValueError(
                "Failed to create user classification preference due to database constraints"
            )

    def get_user_classification_preferences(
        self, user_id: int, dimension_id: Optional[int] = None
    ) -> List[UserClassificationPreference]:
        """
        获取用户分类偏好

        Args:
            user_id: 用户ID
            dimension_id: 分类维度ID，为None表示获取所有维度的偏好

        Returns:
            用户分类偏好列表
        """
        query = (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.user_id == user_id)
            .options(
                joinedload(UserClassificationPreference.dimension),
                joinedload(UserClassificationPreference.value),
            )
        )

        if dimension_id is not None:
            query = query.filter(
                UserClassificationPreference.dimension_id == dimension_id
            )

        return query.order_by(
            UserClassificationPreference.preference_score.desc()
        ).all()

    def get_user_classification_preference_by_id(
        self, preference_id: int
    ) -> Optional[UserClassificationPreference]:
        """
        根据ID获取用户分类偏好

        Args:
            preference_id: 偏好记录ID

        Returns:
            用户分类偏好对象或None
        """
        return (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.id == preference_id)
            .options(
                joinedload(UserClassificationPreference.dimension),
                joinedload(UserClassificationPreference.value),
            )
            .first()
        )

    def update_user_classification_preference(
        self, preference_id: int, update_data: UserClassificationPreferenceUpdate
    ) -> Optional[UserClassificationPreference]:
        """
        更新用户分类偏好

        Args:
            preference_id: 偏好记录ID
            update_data: 更新数据

        Returns:
            更新后的偏好记录或None
        """
        preference = (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.id == preference_id)
            .first()
        )

        if not preference:
            return None

        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if field == "preference_score" and value is not None:
                setattr(preference, field, Decimal(str(value)))
            else:
                setattr(preference, field, value)

        preference.last_updated = datetime.now()

        try:
            self.db.commit()
            self.db.refresh(preference)
            logging.info(f"Updated user classification preference {preference_id}")
            return preference
        except IntegrityError as e:
            self.db.rollback()
            logging.error(f"Failed to update user classification preference: {e}")
            raise ValueError(
                "Failed to update user classification preference due to database constraints"
            )

    def delete_user_classification_preference(self, preference_id: int) -> bool:
        """
        删除用户分类偏好

        Args:
            preference_id: 偏好记录ID

        Returns:
            是否删除成功
        """
        preference = (
            self.db.query(UserClassificationPreference)
            .filter(UserClassificationPreference.id == preference_id)
            .first()
        )

        if not preference:
            return False

        try:
            self.db.delete(preference)
            self.db.commit()
            logging.info(f"Deleted user classification preference {preference_id}")
            return True
        except Exception as e:
            self.db.rollback()
            logging.error(f"Failed to delete user classification preference: {e}")
            return False

    def get_classification_analytics(self) -> Dict[str, Any]:
        """
        获取分类统计分析数据

        Returns:
            分类统计分析数据字典
        """
        try:
            # 获取维度统计
            dimension_count = self.db.query(ClassificationDimension).count()
            active_dimension_count = (
                self.db.query(ClassificationDimension)
                .filter(ClassificationDimension.is_active == True)
                .count()
            )

            # 获取分类值统计
            value_count = self.db.query(ClassificationValue).count()
            active_value_count = (
                self.db.query(ClassificationValue)
                .filter(ClassificationValue.is_active == True)
                .count()
            )

            # 获取用户偏好统计
            preference_count = self.db.query(UserClassificationPreference).count()

            # 获取每个维度的分类值数量
            dimension_value_counts = (
                self.db.query(
                    ClassificationDimension.dimension_name,
                    ClassificationDimension.display_name,
                    func.count(ClassificationValue.id).label("value_count"),
                )
                .outerjoin(ClassificationValue)
                .group_by(
                    ClassificationDimension.id,
                    ClassificationDimension.dimension_name,
                    ClassificationDimension.display_name,
                )
                .all()
            )

            return {
                "summary": {
                    "total_dimensions": dimension_count,
                    "active_dimensions": active_dimension_count,
                    "total_values": value_count,
                    "active_values": active_value_count,
                    "total_preferences": preference_count,
                },
                "dimension_details": [
                    {
                        "dimension_name": row.dimension_name,
                        "display_name": row.display_name,
                        "value_count": row.value_count,
                    }
                    for row in dimension_value_counts
                ],
            }

        except Exception as e:
            logging.error(f"Failed to get classification analytics: {e}")
            return {
                "summary": {
                    "total_dimensions": 0,
                    "active_dimensions": 0,
                    "total_values": 0,
                    "active_values": 0,
                    "total_preferences": 0,
                },
                "dimension_details": [],
            }


class UserInterestService:
    """用户兴趣服务"""

    def __init__(self, db: Session):
        self.db = db
